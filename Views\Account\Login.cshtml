@model LoginViewModel
@{
    ViewData["Title"] = "Đăng nhập";
}

<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="card shadow">
            <div class="card-header bg-primary text-white text-center">
                <h3 class="mb-0">
                    <i class="bi bi-person-circle"></i> Đ<PERSON>ng nhập
                </h3>
            </div>
            <div class="card-body">
                <form asp-action="Login" method="post">
                    <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>

                    <div class="mb-3">
                        <label asp-for="Email" class="form-label"></label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="bi bi-envelope"></i>
                            </span>
                            <input asp-for="Email" class="form-control" placeholder="Nhập email của bạn" />
                        </div>
                        <span asp-validation-for="Email" class="text-danger"></span>
                    </div>

                    <div class="mb-3">
                        <label asp-for="Password" class="form-label"></label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="bi bi-lock"></i>
                            </span>
                            <input asp-for="Password" type="password" class="form-control" placeholder="Nhập mật khẩu" />
                        </div>
                        <span asp-validation-for="Password" class="text-danger"></span>
                    </div>

                    <div class="mb-3 form-check">
                        <input asp-for="RememberMe" class="form-check-input" />
                        <label asp-for="RememberMe" class="form-check-label"></label>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="bi bi-box-arrow-in-right"></i> Đăng nhập
                        </button>
                    </div>
                </form>

                <hr>

                <div class="text-center">
                    <p class="mb-0">
                        Chưa có tài khoản?
                        <a asp-action="Register" class="text-decoration-none">Đăng ký ngay</a>
                    </p>
                </div>
            </div>
        </div>

        <!-- Sample Accounts Info -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">Tài khoản mẫu để test</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-12">
                        <strong>Admin:</strong><br>
                        Email: <EMAIL><br>
                        Password: Admin123!<br><br>

                        <strong>User:</strong><br>
                        Email: <EMAIL><br>
                        Password: User123!<br><br>

                        <strong>Test User:</strong><br>
                        Email: <EMAIL><br>
                        Password: Test123!
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}