@model LoginViewModel
@{
    ViewData["Title"] = "Đăng nhập";
}

<style>
    .login-container {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        margin: -80px -15px -50px -15px;
        padding: 80px 15px 50px 15px;
    }

    .login-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border: none;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        max-width: 400px;
        width: 100%;
    }

    .login-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 2rem;
        text-align: center;
        border: none;
    }

    .login-header h3 {
        color: white;
        font-weight: 600;
        margin: 0;
        font-size: 1.5rem;
    }

    .login-header i {
        font-size: 2.5rem;
        margin-bottom: 0.5rem;
        display: block;
    }

    .login-body {
        padding: 2rem;
    }

    .form-control {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
    }

    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .input-group-text {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 10px 0 0 10px;
    }

    .btn-login {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 10px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .btn-login:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
    }

    .btn-login::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .btn-login:hover::before {
        left: 100%;
    }

    .register-link {
        color: #667eea;
        text-decoration: none;
        font-weight: 600;
        transition: color 0.3s ease;
    }

    .register-link:hover {
        color: #764ba2;
        text-decoration: underline;
    }

    .form-check-input:checked {
        background-color: #667eea;
        border-color: #667eea;
    }

    @@media (max-width: 768px) {
        .login-container {
            padding: 1rem;
        }

        .login-header {
            padding: 1.5rem;
        }

        .login-body {
            padding: 1.5rem;
        }
    }
</style>

<div class="login-container">
    <div class="login-card">
        <div class="login-header">
            <i class="bi bi-person-circle"></i>
            <h3>Đăng nhập</h3>
        </div>
        <div class="login-body">
            <form asp-action="Login" method="post">
                <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>

                <div class="mb-3">
                    <label asp-for="Email" class="form-label fw-semibold">Email</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="bi bi-envelope"></i>
                        </span>
                        <input asp-for="Email" class="form-control" placeholder="Nhập email của bạn" />
                    </div>
                    <span asp-validation-for="Email" class="text-danger small"></span>
                </div>

                <div class="mb-3">
                    <label asp-for="Password" class="form-label fw-semibold">Mật khẩu</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="bi bi-lock"></i>
                        </span>
                        <input asp-for="Password" type="password" class="form-control" placeholder="Nhập mật khẩu" />
                    </div>
                    <span asp-validation-for="Password" class="text-danger small"></span>
                </div>

                <div class="mb-4 form-check">
                    <input asp-for="RememberMe" class="form-check-input" />
                    <label asp-for="RememberMe" class="form-check-label">Ghi nhớ đăng nhập</label>
                </div>

                <div class="d-grid mb-3">
                    <button type="submit" class="btn btn-login btn-lg">
                        <i class="bi bi-box-arrow-in-right me-2"></i>Đăng nhập
                    </button>
                </div>
            </form>

            <div class="text-center">
                <p class="mb-0 text-muted">
                    Chưa có tài khoản?
                    <a asp-action="Register" class="register-link">Đăng ký ngay</a>
                </p>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}