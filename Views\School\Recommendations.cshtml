@model List<School>
@{
    ViewData["Title"] = "Gợi ý trường học";
}

<div class="row mb-4">
    <div class="col-12">
        <h1 class="fw-bold">Gợ<PERSON> ý trường học phù hợp</h1>
        <p class="text-muted">Dựa trên phân tích và đánh giá, đây là những trường học phù hợp với bạn</p>
    </div>
</div>

<!-- Recommendation Algorithm Info -->
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-info">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h5 class="alert-heading mb-2">
                        <i class="bi bi-lightbulb"></i> Thuật toán gợi ý thông minh
                    </h5>
                    <p class="mb-0">
                        Chúng tôi phân tích kết quả khảo sát MBTI, điểm số bài kiểm tra và sở thích cá nhân 
                        để đưa ra những gợi ý trường học phù hợp nhất với bạn.
                    </p>
                </div>
                <div class="col-md-4 text-center">
                    <i class="bi bi-robot display-4 text-primary"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    @if (Model != null && Model.Any())
    {
        @for (int i = 0; i < Model.Count; i++)
        {
            var school = Model[i];
            var rankClass = i == 0 ? "border-warning" : i == 1 ? "border-info" : "border-secondary";
            var rankIcon = i == 0 ? "trophy-fill text-warning" : i == 1 ? "award-fill text-info" : "star-fill text-secondary";
            var matchPercentage = 95 - (i * 10); // Simple algorithm for demo
            
            <div class="col-lg-6 col-md-12 mb-4">
                <div class="card h-100 shadow-sm @rankClass" style="border-width: 2px;">
                    <div class="card-header bg-light">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-@rankIcon fs-4 me-2"></i>
                                <span class="badge bg-primary">#@(i + 1)</span>
                            </div>
                            <div class="text-end">
                                <div class="fw-bold text-success">@matchPercentage% phù hợp</div>
                                <small class="text-muted">Độ phù hợp</small>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <img src="@school.ImageUrl" class="img-fluid rounded" alt="@school.Name" style="height: 100px; object-fit: cover; width: 100%;">
                            </div>
                            <div class="col-md-8">
                                <h5 class="card-title">@school.Name</h5>
                                <p class="card-text">@school.Description</p>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <div class="row">
                                <div class="col-6">
                                    <small class="text-muted">
                                        <i class="bi bi-geo-alt"></i> @school.Address
                                    </small>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">
                                        <i class="bi bi-telephone"></i> @school.PhoneNumber
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <div class="progress mb-2" style="height: 8px;">
                                <div class="progress-bar bg-success" role="progressbar" 
                                     style="width: @matchPercentage%" 
                                     aria-valuenow="@matchPercentage" aria-valuemin="0" aria-valuemax="100">
                                </div>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span class="text-primary fw-bold">
                                    @school.TuitionFee.ToString("C0", new System.Globalization.CultureInfo("vi-VN"))
                                </span>
                                <span class="text-success fw-bold">@matchPercentage% Match</span>
                            </div>
                        </div>
                        
                        <!-- Recommendation Reasons -->
                        <div class="mt-3">
                            <h6>Lý do gợi ý:</h6>
                            <ul class="list-unstyled">
                                @if (i == 0)
                                {
                                    <li><i class="bi bi-check-circle text-success"></i> Phù hợp với tính cách MBTI của bạn</li>
                                    <li><i class="bi bi-check-circle text-success"></i> Học phí hợp lý</li>
                                    <li><i class="bi bi-check-circle text-success"></i> Chương trình đào tạo chất lượng</li>
                                }
                                else if (i == 1)
                                {
                                    <li><i class="bi bi-check-circle text-info"></i> Môi trường học tập tốt</li>
                                    <li><i class="bi bi-check-circle text-info"></i> Cơ sở vật chất hiện đại</li>
                                }
                                else
                                {
                                    <li><i class="bi bi-check-circle text-secondary"></i> Vị trí thuận lợi</li>
                                    <li><i class="bi bi-check-circle text-secondary"></i> Danh tiếng tốt</li>
                                }
                            </ul>
                        </div>
                        
                        <div class="mt-auto">
                            <div class="d-grid gap-2">
                                <a asp-action="Details" asp-route-id="@school.Id" class="btn btn-primary">
                                    <i class="bi bi-info-circle"></i> Xem chi tiết
                                </a>
                                @if (!string.IsNullOrEmpty(school.Website))
                                {
                                    <a href="@school.Website" target="_blank" class="btn btn-outline-secondary">
                                        <i class="bi bi-globe"></i> Website chính thức
                                    </a>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        }
    }
    else
    {
        <div class="col-12 text-center">
            <div class="alert alert-warning">
                <i class="bi bi-exclamation-triangle"></i> 
                Hiện tại chưa có gợi ý trường học. Hãy hoàn thành khảo sát MBTI để nhận gợi ý phù hợp.
            </div>
            <a asp-controller="Survey" asp-action="Index" class="btn btn-primary btn-lg">
                <i class="bi bi-clipboard-check"></i> Làm khảo sát MBTI
            </a>
        </div>
    }
</div>

<!-- How it works section -->
<div class="row mt-5">
    <div class="col-12">
        <h3>Cách thức hoạt động</h3>
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="text-center">
                    <i class="bi bi-person-check display-4 text-primary"></i>
                    <h5 class="mt-2">1. Phân tích tính cách</h5>
                    <p class="text-muted">Dựa trên kết quả MBTI của bạn</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="text-center">
                    <i class="bi bi-graph-up display-4 text-success"></i>
                    <h5 class="mt-2">2. Đánh giá năng lực</h5>
                    <p class="text-muted">Từ kết quả các bài kiểm tra</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="text-center">
                    <i class="bi bi-cpu display-4 text-info"></i>
                    <h5 class="mt-2">3. Thuật toán AI</h5>
                    <p class="text-muted">Xử lý và phân tích dữ liệu</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="text-center">
                    <i class="bi bi-bullseye display-4 text-warning"></i>
                    <h5 class="mt-2">4. Gợi ý chính xác</h5>
                    <p class="text-muted">Đưa ra kết quả phù hợp nhất</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Action buttons -->
<div class="row mt-4">
    <div class="col-12 text-center">
        <a asp-action="Index" class="btn btn-outline-primary btn-lg me-2">
            <i class="bi bi-building"></i> Xem tất cả trường học
        </a>
        <a asp-controller="Survey" asp-action="Index" class="btn btn-success btn-lg">
            <i class="bi bi-arrow-clockwise"></i> Làm lại khảo sát
        </a>
    </div>
</div>
