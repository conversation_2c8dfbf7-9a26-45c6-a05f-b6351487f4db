using System.ComponentModel.DataAnnotations;

namespace MyAspNetCoreApp.Models
{
    public class School
    {
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string Location { get; set; } = string.Empty;

        [StringLength(200)]
        public string Website { get; set; } = string.Empty;

        public decimal? TuitionFee { get; set; }

        [StringLength(200)]
        public string ImageUrl { get; set; } = string.Empty;

        public int EstablishedYear { get; set; }

        [Required]
        [StringLength(50)]
        public string Type { get; set; } = string.Empty; // <PERSON><PERSON><PERSON> lập, <PERSON><PERSON> thục, <PERSON><PERSON> lập

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual ICollection<ExamResult> ExamResults { get; set; } = new List<ExamResult>();
    }
}
