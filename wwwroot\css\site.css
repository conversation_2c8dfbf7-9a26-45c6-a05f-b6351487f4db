html {
  font-size: 14px;
  scroll-behavior: smooth;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
}

.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

/* Hero Section Styles */
.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 400px;
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="1" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="1" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
}

/* Card Styles */
.card {
  border: none;
  border-radius: 15px;
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.card-header {
  border-radius: 15px 15px 0 0 !important;
  border: none;
}

/* Button Styles */
.btn {
  border-radius: 25px;
  padding: 0.5rem 1.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-lg {
  padding: 0.75rem 2rem;
}

.btn-primary {
  background: linear-gradient(45deg, #007bff, #0056b3);
  border: none;
}

.btn-primary:hover {
  background: linear-gradient(45deg, #0056b3, #004085);
  transform: translateY(-2px);
}

.btn-success {
  background: linear-gradient(45deg, #28a745, #1e7e34);
  border: none;
}

.btn-success:hover {
  background: linear-gradient(45deg, #1e7e34, #155724);
  transform: translateY(-2px);
}

/* Navigation Styles */
.navbar-brand {
  font-weight: bold;
  font-size: 1.5rem;
}

.navbar-nav .nav-link {
  font-weight: 500;
  margin: 0 0.5rem;
  border-radius: 20px;
  transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
  background-color: rgba(255,255,255,0.1);
}

/* Form Styles */
.form-control {
  border-radius: 10px;
  border: 2px solid #e9ecef;
  transition: all 0.3s ease;
}

.form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.form-check-input:checked {
  background-color: #007bff;
  border-color: #007bff;
}

/* Question Container Styles */
.question-container {
  background: #f8f9fa;
  border-radius: 15px;
  transition: all 0.3s ease;
}

.question-container:hover {
  background: #e9ecef;
}

.question-title {
  color: #495057;
  margin-bottom: 1rem;
}

/* Progress Bar Styles */
.progress {
  border-radius: 10px;
  height: 10px;
}

.progress-bar {
  border-radius: 10px;
  transition: width 0.6s ease;
}

/* Badge Styles */
.badge {
  border-radius: 15px;
  padding: 0.5em 0.75em;
}

/* Footer Styles */
footer {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.6s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.slide-up {
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from { transform: translateY(30px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* Responsive Styles */
@media (max-width: 768px) {
  .hero-section {
    min-height: 300px;
  }

  .btn-lg {
    padding: 0.5rem 1.5rem;
    font-size: 1rem;
  }

  .display-4 {
    font-size: 2rem;
  }
}

/* Utility Classes */
.text-gradient {
  background: linear-gradient(45deg, #007bff, #6f42c1);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.shadow-custom {
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.border-gradient {
  border: 2px solid;
  border-image: linear-gradient(45deg, #007bff, #6f42c1) 1;
}

html {
  position: relative;
  min-height: 100%;
}

body {
  margin-bottom: 60px;
}

.form-floating > .form-control-plaintext::placeholder, .form-floating > .form-control::placeholder {
  color: var(--bs-secondary-color);
  text-align: end;
}

.form-floating > .form-control-plaintext:focus::placeholder, .form-floating > .form-control:focus::placeholder {
  text-align: start;
}