@model School
@{
    ViewData["Title"] = "Thêm trường học mới";
}

<div class="row mb-4">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    <a asp-action="Index" asp-controller="Dashboard">Dashboard</a>
                </li>
                <li class="breadcrumb-item">
                    <a asp-action="Index">Quản lý trường học</a>
                </li>
                <li class="breadcrumb-item active">Thêm mới</li>
            </ol>
        </nav>
        
        <h1 class="fw-bold">
            <i class="bi bi-plus-circle"></i> Thêm trường học mới
        </h1>
        <p class="text-muted">Nhập thông tin chi tiết về trường đại học</p>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-info-circle"></i> Thông tin cơ bản
                </h5>
            </div>
            <div class="card-body">
                <form asp-action="Create" method="post">
                    <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                    
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label asp-for="Name" class="form-label"></label>
                            <input asp-for="Name" class="form-control" placeholder="Ví dụ: Đại học Bách khoa Hà Nội" />
                            <span asp-validation-for="Name" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="PhoneNumber" class="form-label">Số điện thoại</label>
                            <input asp-for="PhoneNumber" class="form-control" placeholder="024-1234-5678" />
                            <span asp-validation-for="PhoneNumber" class="text-danger"></span>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label asp-for="Description" class="form-label"></label>
                        <textarea asp-for="Description" class="form-control" rows="4" 
                                  placeholder="Mô tả về trường học, các ngành đào tạo, thế mạnh..."></textarea>
                        <span asp-validation-for="Description" class="text-danger"></span>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label asp-for="Address" class="form-label">Địa chỉ</label>
                            <input asp-for="Address" class="form-control" placeholder="Ví dụ: 1 Đại Cồ Việt, Hà Nội" />
                            <span asp-validation-for="Address" class="text-danger"></span>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label asp-for="EstablishedYear" class="form-label"></label>
                            <input asp-for="EstablishedYear" type="number" class="form-control"
                                   min="1900" max="@DateTime.Now.Year" placeholder="@DateTime.Now.Year" />
                            <span asp-validation-for="EstablishedYear" class="text-danger"></span>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label asp-for="Website" class="form-label"></label>
                            <input asp-for="Website" type="url" class="form-control" 
                                   placeholder="https://example.edu.vn" />
                            <span asp-validation-for="Website" class="text-danger"></span>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label asp-for="TuitionFee" class="form-label"></label>
                            <div class="input-group">
                                <input asp-for="TuitionFee" type="number" class="form-control" 
                                       placeholder="20000000" step="100000" />
                                <span class="input-group-text">VNĐ</span>
                            </div>
                            <span asp-validation-for="TuitionFee" class="text-danger"></span>
                            <div class="form-text">Học phí trung bình mỗi năm</div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label asp-for="ImageUrl" class="form-label"></label>
                        <input asp-for="ImageUrl" type="url" class="form-control" 
                               placeholder="https://example.com/image.jpg" />
                        <span asp-validation-for="ImageUrl" class="text-danger"></span>
                        <div class="form-text">URL hình ảnh đại diện cho trường học</div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> Quay lại
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> Thêm trường học
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-lightbulb"></i> Hướng dẫn
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>Tên trường:</strong>
                    <p class="small text-muted">Nhập tên đầy đủ và chính thức của trường đại học.</p>
                </div>
                
                <div class="mb-3">
                    <strong>Mô tả:</strong>
                    <p class="small text-muted">Viết mô tả ngắn gọn về trường, các ngành đào tạo chính và thế mạnh.</p>
                </div>
                
                <div class="mb-3">
                    <strong>Học phí:</strong>
                    <p class="small text-muted">Nhập học phí trung bình mỗi năm học (VNĐ).</p>
                </div>
                
                <div class="mb-3">
                    <strong>Hình ảnh:</strong>
                    <p class="small text-muted">Sử dụng URL hình ảnh có chất lượng tốt, tỷ lệ 16:9.</p>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-eye"></i> Xem trước
                </h6>
            </div>
            <div class="card-body">
                <div id="preview" class="text-center">
                    <div class="bg-light rounded p-3">
                        <i class="bi bi-building display-4 text-muted"></i>
                        <p class="text-muted mt-2">Xem trước sẽ hiển thị ở đây khi bạn nhập thông tin</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        // Live preview functionality
        function updatePreview() {
            const name = document.getElementById('Name').value || 'Tên trường học';
            const type = document.getElementById('Type').value || 'Loại hình';
            const location = document.getElementById('Location').value || 'Địa điểm';
            const imageUrl = document.getElementById('ImageUrl').value;
            
            let preview = '<div class="card">';
            
            if (imageUrl) {
                preview += `<img src="${imageUrl}" class="card-img-top" style="height: 120px; object-fit: cover;" onerror="this.style.display='none'">`;
            }
            
            preview += '<div class="card-body">';
            preview += `<h6 class="card-title">${name}</h6>`;
            preview += `<p class="card-text"><small class="text-muted"><i class="bi bi-geo-alt"></i> ${location}</small></p>`;
            preview += `<span class="badge bg-primary">${type}</span>`;
            preview += '</div></div>';
            
            document.getElementById('preview').innerHTML = preview;
        }
        
        // Add event listeners
        ['Name', 'Type', 'Location', 'ImageUrl'].forEach(id => {
            document.getElementById(id).addEventListener('input', updatePreview);
        });
        
        // Initial preview
        updatePreview();
    </script>
}
