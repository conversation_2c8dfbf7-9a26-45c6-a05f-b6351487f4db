using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyAspNetCoreApp.Models;

namespace MyAspNetCoreApp.Controllers
{
    public class HomeController : Controller
    {
        private readonly CareerGuideDbContext _context;

        public HomeController(CareerGuideDbContext context)
        {
            _context = context;
        }

        public async Task<IActionResult> Index()
        {
            var featuredSchools = await _context.Products
                .Where(p => p.IsFeatured)
                .OrderBy(p => p.Id)
                .Take(6)
                .ToListAsync();

            return View(featuredSchools);
        }

        public IActionResult Privacy()
        {
            return View();
        }

        public IActionResult Error()
        {
            return View();
        }

        public IActionResult MbtiAssessment()
        {
            return View();
        }

        public IActionResult SchoolRecommendations()
        {
            return View();
        }
    }
}