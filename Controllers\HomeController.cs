using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyAspNetCoreApp.Models;
using System.Diagnostics;

namespace MyAspNetCoreApp.Controllers
{
    public class HomeController : Controller
    {
        private readonly CareerGuideDbContext _context;

        public HomeController(CareerGuideDbContext context)
        {
            _context = context;
        }

        public async Task<IActionResult> Index()
        {
            var featuredSchools = await _context.Products
                .Where(p => p.IsFeatured)
                .OrderBy(p => p.Id)
                .Take(6)
                .ToListAsync();

            return View(featuredSchools);
        }

        public IActionResult Privacy()
        {
            return View();
        }

        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult Error()
        {
            var errorViewModel = new ErrorViewModel
            {
                RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier,
                StatusCode = HttpContext.Response.StatusCode
            };

            return View(errorViewModel);
        }

        public IActionResult MbtiAssessment()
        {
            return View();
        }

        public IActionResult SchoolRecommendations()
        {
            return View();
        }
    }
}