using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyAspNetCoreApp.Models;

namespace MyAspNetCoreApp.Areas.Admin.Controllers
{
    [Area("Admin")]
    [Authorize(Roles = "Admin")]
    public class SchoolManagementController : Controller
    {
        private readonly CareerGuideDbContext _context;

        public SchoolManagementController(CareerGuideDbContext context)
        {
            _context = context;
        }

        // GET: Admin/SchoolManagement
        public async Task<IActionResult> Index()
        {
            var schools = await _context.Schools.ToListAsync();
            return View(schools);
        }

        // GET: Admin/SchoolManagement/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var school = await _context.Schools.FirstOrDefaultAsync(m => m.Id == id);
            if (school == null)
            {
                return NotFound();
            }

            return View(school);
        }

        // GET: Admin/SchoolManagement/Create
        public IActionResult Create()
        {
            return View();
        }

        // POST: Admin/SchoolManagement/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("Name,Description,Location,Website,TuitionFee,ImageUrl,EstablishedYear,Type")] School school)
        {
            if (ModelState.IsValid)
            {
                school.CreatedDate = DateTime.Now;
                _context.Add(school);
                await _context.SaveChangesAsync();
                TempData["Success"] = "Trường học đã được thêm thành công.";
                return RedirectToAction(nameof(Index));
            }
            return View(school);
        }

        // GET: Admin/SchoolManagement/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var school = await _context.Schools.FindAsync(id);
            if (school == null)
            {
                return NotFound();
            }
            return View(school);
        }

        // POST: Admin/SchoolManagement/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,Name,Description,Location,Website,TuitionFee,ImageUrl,EstablishedYear,Type,CreatedDate")] School school)
        {
            if (id != school.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(school);
                    await _context.SaveChangesAsync();
                    TempData["Success"] = "Thông tin trường học đã được cập nhật.";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!SchoolExists(school.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }
            return View(school);
        }

        // GET: Admin/SchoolManagement/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var school = await _context.Schools.FirstOrDefaultAsync(m => m.Id == id);
            if (school == null)
            {
                return NotFound();
            }

            return View(school);
        }

        // POST: Admin/SchoolManagement/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var school = await _context.Schools.FindAsync(id);
            if (school != null)
            {
                _context.Schools.Remove(school);
                await _context.SaveChangesAsync();
                TempData["Success"] = "Trường học đã được xóa.";
            }

            return RedirectToAction(nameof(Index));
        }

        private bool SchoolExists(int id)
        {
            return _context.Schools.Any(e => e.Id == id);
        }
    }
}
