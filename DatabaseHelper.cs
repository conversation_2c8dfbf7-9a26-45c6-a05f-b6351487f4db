using Microsoft.EntityFrameworkCore;
using MyAspNetCoreApp.Models;

namespace MyAspNetCoreApp
{
    public static class DatabaseHelper
    {
        public static async Task EnsureSchoolColumnsExist(CareerGuideDbContext context)
        {
            try
            {
                // Try to execute a simple query to check if columns exist
                await context.Database.ExecuteSqlRawAsync(@"
                    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Schools]') AND name = 'Type')
                    BEGIN
                        ALTER TABLE [Schools] ADD [Type] nvarchar(50) NOT NULL DEFAULT '';
                    END
                ");

                await context.Database.ExecuteSqlRawAsync(@"
                    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Schools]') AND name = 'Location')
                    BEGIN
                        ALTER TABLE [Schools] ADD [Location] nvarchar(100) NOT NULL DEFAULT '';
                    END
                ");

                await context.Database.ExecuteSqlRawAsync(@"
                    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Schools]') AND name = 'CreatedDate')
                    BEGIN
                        ALTER TABLE [Schools] ADD [CreatedDate] datetime2 NOT NULL DEFAULT GETDATE();
                    END
                ");

                // Update existing records
                await context.Database.ExecuteSqlRawAsync(@"
                    UPDATE [Schools] 
                    SET [Type] = CASE WHEN [Type] = '' OR [Type] IS NULL THEN 'Đại học' ELSE [Type] END,
                        [Location] = CASE WHEN [Location] = '' OR [Location] IS NULL THEN 'Hà Nội' ELSE [Location] END,
                        [CreatedDate] = CASE WHEN [CreatedDate] = '1900-01-01' OR [CreatedDate] IS NULL THEN GETDATE() ELSE [CreatedDate] END
                ");
            }
            catch (Exception ex)
            {
                // Columns might already exist, ignore error
                Console.WriteLine($"Database schema update: {ex.Message}");
            }
        }
    }
}
