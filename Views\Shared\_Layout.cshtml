<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Career Guide</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link rel="stylesheet" href="~/css/site.css" />
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" asp-controller="Home" asp-action="Index">
                    <i class="bi bi-mortarboard"></i> Career Guide
                </a>

                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Home" asp-action="Index">Trang chủ</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="School" asp-action="Index">Trường học</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Exam" asp-action="Index">Bài kiểm tra</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Survey" asp-action="Index">Khảo sát MBTI</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="School" asp-action="Recommendations">Gợi ý trường</a>
                        </li>
                    </ul>

                    <ul class="navbar-nav">
                        @if (User.Identity?.IsAuthenticated == true)
                        {
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" id="accountDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="bi bi-person-circle"></i> @User.Identity.Name
                                </a>
                                <ul class="dropdown-menu" aria-labelledby="accountDropdown">
                                    <li><a class="dropdown-item" asp-controller="Exam" asp-action="MyResults">Kết quả thi</a></li>
                                    <li><a class="dropdown-item" asp-controller="Survey" asp-action="MyResults">Kết quả khảo sát</a></li>
                                    @if (User.IsInRole("Admin"))
                                    {
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" asp-area="Admin" asp-controller="Dashboard" asp-action="Index">Quản trị</a></li>
                                    }
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <form asp-controller="Account" asp-action="Logout" method="post" class="d-inline">
                                            <button type="submit" class="dropdown-item">Đăng xuất</button>
                                        </form>
                                    </li>
                                </ul>
                            </li>
                        }
                        else
                        {
                            <li class="nav-item">
                                <a class="nav-link" asp-controller="Account" asp-action="Login">Đăng nhập</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" asp-controller="Account" asp-action="Register">Đăng ký</a>
                            </li>
                        }
                    </ul>
                </div>
            </div>
        </nav>
    </header>
    <main role="main" class="pb-3">
        <div class="container">
            @RenderBody()
        </div>
    </main>

    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>Career Guide</h5>
                    <p>Hệ thống hướng nghiệp và tư vấn giáo dục</p>
                </div>
                <div class="col-md-6">
                    <h5>Liên hệ</h5>
                    <p>Email: <EMAIL><br>
                    Điện thoại: 0123-456-789</p>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; 2025 - Career Guide. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
