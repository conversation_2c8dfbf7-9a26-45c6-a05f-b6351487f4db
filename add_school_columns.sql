-- Add missing columns to Schools table
USE [CareerGuideDb]
GO

-- Check if columns exist before adding
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Schools]') AND name = 'Type')
BEGIN
    ALTER TABLE [Schools] ADD [Type] nvarchar(50) NOT NULL DEFAULT '';
    PRINT 'Added Type column';
END
ELSE
BEGIN
    PRINT 'Type column already exists';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Schools]') AND name = 'Location')
BEGIN
    ALTER TABLE [Schools] ADD [Location] nvarchar(100) NOT NULL DEFAULT '';
    PRINT 'Added Location column';
END
ELSE
BEGIN
    PRINT 'Location column already exists';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Schools]') AND name = 'CreatedDate')
BEGIN
    ALTER TABLE [Schools] ADD [CreatedDate] datetime2 NOT NULL DEFAULT GETDATE();
    PRINT 'Added CreatedDate column';
END
ELSE
BEGIN
    PRINT 'CreatedDate column already exists';
END

-- Update existing records with default values
UPDATE [Schools] 
SET [Type] = 'Đại học', 
    [Location] = 'Hà Nội',
    [CreatedDate] = GETDATE()
WHERE [Type] = '' OR [Location] = '' OR [CreatedDate] = '1900-01-01';

PRINT 'Updated existing records with default values';
