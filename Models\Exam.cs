using System.ComponentModel.DataAnnotations;

namespace MyAspNetCoreApp.Models
{
    public class Exam
    {
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string Title { get; set; } = string.Empty;

        [Required]
        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        public DateTime CreatedDate { get; set; }

        public int Duration { get; set; } // Duration in minutes

        public int TotalQuestions { get; set; }

        public double PassingScore { get; set; } = 60.0; // Passing score percentage

        public bool IsActive { get; set; } = true;

        // Navigation properties
        public virtual ICollection<Question> Questions { get; set; } = new List<Question>();
        public virtual ICollection<ExamResult> ExamResults { get; set; } = new List<ExamResult>();
    }
}
