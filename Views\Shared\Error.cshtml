@model ErrorViewModel
@{
    ViewData["Title"] = "Lỗi";
}

<style>
    .error-container {
        min-height: 80vh;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        margin: -15px;
        padding: 2rem 15px;
    }
    
    .error-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border: none;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        max-width: 500px;
        width: 100%;
        text-align: center;
    }
    
    .error-header {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        padding: 2rem;
        color: white;
    }
    
    .error-icon {
        font-size: 4rem;
        margin-bottom: 1rem;
    }
    
    .error-body {
        padding: 2rem;
    }
    
    .btn-home {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 10px;
        padding: 0.75rem 2rem;
        color: white;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
        display: inline-block;
    }
    
    .btn-home:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        color: white;
        text-decoration: none;
    }
</style>

<div class="error-container">
    <div class="error-card">
        <div class="error-header">
            <i class="bi bi-exclamation-triangle error-icon"></i>
            <h2>Oops! Có lỗi xảy ra</h2>
        </div>
        <div class="error-body">
            @if (Model?.ShowRequestId == true)
            {
                <p class="text-muted mb-3">
                    <strong>Request ID:</strong> @Model.RequestId
                </p>
            }
            
            <h4 class="text-danger mb-3">Xin lỗi vì sự bất tiện này!</h4>
            <p class="text-muted mb-4">
                Đã xảy ra lỗi không mong muốn. Vui lòng thử lại sau hoặc liên hệ với chúng tôi nếu vấn đề vẫn tiếp tục.
            </p>
            
            <div class="d-flex gap-2 justify-content-center">
                <a href="javascript:history.back()" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left me-2"></i>Quay lại
                </a>
                <a href="/" class="btn-home">
                    <i class="bi bi-house me-2"></i>Về trang chủ
                </a>
            </div>
        </div>
    </div>
</div>
