@model RegisterViewModel
@{
    ViewData["Title"] = "Đăng ký";
}

<div class="row justify-content-center">
    <div class="col-md-6 col-lg-5">
        <div class="card shadow">
            <div class="card-header bg-success text-white text-center">
                <h3 class="mb-0">
                    <i class="bi bi-person-plus"></i> Đăng ký tài k<PERSON>ản
                </h3>
            </div>
            <div class="card-body">
                <form asp-action="Register" method="post">
                    <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                    
                    <div class="mb-3">
                        <label asp-for="FullName" class="form-label"></label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="bi bi-person"></i>
                            </span>
                            <input asp-for="FullName" class="form-control" placeholder="Nhập họ và tên" />
                        </div>
                        <span asp-validation-for="FullName" class="text-danger"></span>
                    </div>
                    
                    <div class="mb-3">
                        <label asp-for="Email" class="form-label"></label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="bi bi-envelope"></i>
                            </span>
                            <input asp-for="Email" class="form-control" placeholder="Nhập email của bạn" />
                        </div>
                        <span asp-validation-for="Email" class="text-danger"></span>
                    </div>
                    
                    <div class="mb-3">
                        <label asp-for="Password" class="form-label"></label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="bi bi-lock"></i>
                            </span>
                            <input asp-for="Password" type="password" class="form-control" placeholder="Nhập mật khẩu" />
                        </div>
                        <span asp-validation-for="Password" class="text-danger"></span>
                        <div class="form-text">
                            Mật khẩu phải có ít nhất 6 ký tự, bao gồm chữ hoa, chữ thường và số.
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label asp-for="ConfirmPassword" class="form-label"></label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="bi bi-lock-fill"></i>
                            </span>
                            <input asp-for="ConfirmPassword" type="password" class="form-control" placeholder="Nhập lại mật khẩu" />
                        </div>
                        <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-success btn-lg">
                            <i class="bi bi-person-plus"></i> Đăng ký
                        </button>
                    </div>
                </form>
                
                <hr>
                
                <div class="text-center">
                    <p class="mb-0">
                        Đã có tài khoản? 
                        <a asp-action="Login" class="text-decoration-none">Đăng nhập ngay</a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
